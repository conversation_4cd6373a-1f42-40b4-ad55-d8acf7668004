// 延迟加载logger，避免循环依赖
let logger = null
function getLogger() {
  if (!logger) {
    try {
      logger = require('./AsyncLogger')
    } catch (e) {
      // 如果AsyncLogger还未加载，返回null
      return null
    }
  }
  return logger
}

/**
 * 游戏配置模块
 * 统一管理游戏的各种配置项
 */
class GameConfig {
  constructor() {
    // 开发者模式配置 - 从本地存储或默认值加载
    this.developerMode = this.loadDeveloperModeFromStorage()

    // 游戏基础配置
    this.gameSettings = {
      maxSlots: 7,           // 最大槽位数
      matchCount: 3,         // 消除所需数量
      animationDuration: 300, // 动画时长(ms)
      levelUnlockMode: this.developerMode ? 'all' : 'sequential' // 开发者模式下解锁所有关卡
    }

    // 调试配置
    this.debugSettings = {
      enableConsoleLog: this.developerMode,
      showFPS: false,
      showHitboxes: false,
      enableDevMenu: this.developerMode
    }

    // 网络配置
    this.networkSettings = {
      apiTimeout: 5000,
      retryCount: 3,
      enableServerSync: !this.developerMode // 开发者模式下禁用服务器同步
    }

    // 初始化日志级别（延迟执行，确保AsyncLogger已加载）
    setTimeout(() => {
      this.initializeLogLevel()
    }, 0)

    console.log(`🎮 GameConfig初始化完成 - 开发者模式: ${this.developerMode}`)
  }

  /**
   * 从本地存储加载开发者模式配置
   * @returns {boolean}
   */
  loadDeveloperModeFromStorage() {
    try {
      // // 尝试从微信小游戏存储读取
      // if (typeof wx !== 'undefined' && wx.getStorageSync) {
      //   const config = wx.getStorageSync('gameConfig')
      //   if (config && config.developerMode !== undefined) {
      //     console.log('📱 从本地存储加载开发者模式:', config.developerMode)
      //     return config.developerMode
      //   }
      // }

      // // 如果没有存储的配置，使用默认值
      // console.log('🔧 使用默认开发者模式配置: false')
      return true // 默认关闭开发者模式，生产环境安全
    } catch (error) {
      console.warn('⚠️ 加载开发者模式配置失败，使用默认值:', error)
      return false
    }
  }

  /**
   * 保存开发者模式配置到本地存储
   */
  saveDeveloperModeToStorage() {
    try {
      if (typeof wx !== 'undefined' && wx.setStorageSync) {
        wx.setStorageSync('gameConfig', { developerMode: this.developerMode })
        console.log('💾 开发者模式配置已保存到本地存储')
      }
    } catch (error) {
      console.error('❌ 保存开发者模式配置失败:', error)
    }
  }

  /**
   * 初始化日志级别
   */
  initializeLogLevel() {
    const asyncLogger = getLogger()
    if (asyncLogger && typeof asyncLogger.setLevelByDeveloperMode === 'function') {
      asyncLogger.setLevelByDeveloperMode(this.developerMode)
    }
  }
  
  /**
   * 获取开发者模式状态
   * @returns {boolean}
   */
  isDeveloperMode() {
    return this.developerMode
  }
  
  /**
   * 设置开发者模式
   * @param {boolean} enabled
   */
  setDeveloperMode(enabled) {
    const wasEnabled = this.developerMode
    this.developerMode = enabled

    // 更新相关配置
    this.networkSettings.enableServerSync = !enabled
    this.debugSettings.enableConsoleLog = enabled
    this.debugSettings.enableDevMenu = enabled
    this.gameSettings.levelUnlockMode = enabled ? 'all' : 'sequential'

    // 保存到本地存储
    this.saveDeveloperModeToStorage()

    // 通知AsyncLogger更新日志级别
    const asyncLogger = getLogger()
    if (asyncLogger && typeof asyncLogger.setLevelByDeveloperMode === 'function') {
      asyncLogger.setLevelByDeveloperMode(enabled)
    } else {
      // 如果AsyncLogger不可用，直接输出到控制台
      console.log(`🛠️ 开发者模式${enabled ? '已启用' : '已禁用'}`)
    }

    // 如果是首次设置或状态发生变化，输出提示信息
    if (wasEnabled !== enabled) {
      if (enabled) {
        console.log('🛠️ 开发者模式已启用：')
        console.log('   - 日志级别：DEBUG（显示DEBUG/INFO/WARN/ERROR日志）')
        console.log('   - 关卡解锁：所有关卡已解锁')
        console.log('   - 服务器同步：已禁用')
        console.log('   - 开发菜单：已启用')
      } else {
        console.log('🔒 开发者模式已禁用：')
        console.log('   - 日志级别：WARN（仅显示WARN/ERROR日志）')
        console.log('   - 关卡解锁：按顺序解锁')
        console.log('   - 服务器同步：已启用')
        console.log('   - 开发菜单：已禁用')
      }
    }
  }

  /**
   * 切换开发者模式
   * @returns {boolean} 新的开发者模式状态
   */
  toggleDeveloperMode() {
    this.setDeveloperMode(!this.developerMode)
    return this.developerMode
  }

  /**
   * 检查关卡是否已解锁
   * @param {number} level - 关卡编号
   * @param {number} maxUnlockedLevel - 当前最高解锁关卡
   * @returns {boolean} 是否已解锁
   */
  isLevelUnlocked(level, maxUnlockedLevel = 1) {
    // 开发者模式下所有关卡都解锁
    if (this.developerMode) {
      return true
    }

    // 非开发者模式下按顺序解锁
    return level <= maxUnlockedLevel
  }
  
  /**
   * 获取游戏设置
   * @returns {Object}
   */
  getGameSettings() {
    return { ...this.gameSettings }
  }
  
  /**
   * 获取调试设置
   * @returns {Object}
   */
  getDebugSettings() {
    return { ...this.debugSettings }
  }
  
  /**
   * 获取网络设置
   * @returns {Object}
   */
  getNetworkSettings() {
    return { ...this.networkSettings }
  }
  
  /**
   * 是否启用服务器同步
   * @returns {boolean}
   */
  isServerSyncEnabled() {
    return this.networkSettings.enableServerSync
  }
}

// 创建全局配置实例
const gameConfig = new GameConfig()

// 导出配置实例
if (typeof module !== 'undefined' && module.exports) {
  module.exports = gameConfig
  // 同时设置全局变量（在测试时有用）
  if (typeof global !== 'undefined') {
    global.gameConfig = gameConfig
  }
} else if (typeof window !== 'undefined') {
  window.gameConfig = gameConfig
} 