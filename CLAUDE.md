# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is "鸭了个鸭" (<PERSON>) - a WeChat mini-game that mimics the popular "羊了个羊" (Sheep a Sheep) gameplay. It's a match-3 puzzle game where players click blocks to move them to slots, and when 3 identical blocks gather in the slots, they are eliminated.

## Architecture

### Core Structure
- **Entry Point**: `game.js` - Loads configuration and initializes the Main class
- **Main Controller**: `js/main.js` - Manages game flow, Canvas rendering, and user interactions
- **Game Logic**: 
  - `js/game/Block.js` - Individual block entities with position, type, and animations
  - `js/game/Board.js` - Game board managing block layout and layer hierarchy
  - `js/game/Slot.js` - Bottom container managing match-3 elimination logic
- **UI Components**: `js/ui/` - Buttons, panels, and interactive elements
- **Utilities**: `js/utils/` - Resource loading, event management, audio, save management, etc.

### Key Classes
- **Main**: Primary game controller managing the entire game flow
- **Board**: Handles block positioning, layer management, and click detection
- **Slot**: Manages the bottom 7-slot container and elimination logic
- **Block**: Individual game pieces with animation and state management
- **GameConfig**: Centralized configuration management with developer mode toggle

### Modular Design
The codebase uses a modular architecture with clear separation of concerns:
- Game logic is separated from rendering
- UI components are self-contained
- Utilities provide shared functionality across modules
- Configuration is centrally managed

## Development Commands

### WeChat Developer Tools Setup
1. Open WeChat Developer Tools
2. Select "Mini Game" project type
3. Import this project directory
4. Set project configuration with test AppID
5. Click "Compile" to run the game

### Audio Generation (Development Tool)
```bash
node tools/create_simple_sounds.js
```
Generates basic WAV audio files for testing purposes.

### Debug Mode Configuration
- Set `DEBUG_MODE = true` in `game.js` for detailed console logging
- Set `developerMode = true` in `js/utils/GameConfig.js` to enable developer features
- Developer mode disables server synchronization and enables additional debugging features

## Configuration Files

- `game.json` - WeChat mini-game runtime configuration (orientation, timeouts, debug settings)
- `project.config.json` - WeChat Developer Tools project settings (compilation, AppID, library version)
- `js/utils/GameConfig.js` - Game-specific configuration (slots, match count, animation duration)

## Key Features

### Game Mechanics
- 7-slot elimination system (configurable via GameConfig)
- 3-match elimination logic (configurable)
- Multi-layer block stacking with visibility rules
- Power-ups: Remove, Undo, Shuffle (limited uses)
- Move history tracking for undo functionality

### Technical Features
- Canvas 2D rendering with screen adaptation
- Touch interaction with debouncing
- Animation system for smooth block movements
- Resource loading system for images and audio
- Save/load game state functionality
- Error handling and user feedback

### Current Implementation Status
✅ Core game framework, Canvas rendering, block system, match-3 logic, touch interaction, level system
🚧 Image resources, audio system, power-up implementations, particle effects, data persistence

## File Structure Notes

- Resources: `images/` (cartoon animals), `audio/` (game sounds)
- The game uses a custom image generation system as fallback when resources are missing
- WeChat mini-game specific files: `game.js`, `game.json`, `project.config.json`
- Tools directory contains development utilities for asset generation