const logger = require('AsyncLogger')

/**
 * 错误处理模块
 * 处理微信小游戏API相关的错误和异常
 */
class ErrorHandler {
  constructor() {
    this.errorLog = []
    this.maxLogSize = 100
    this.setupGlobalErrorHandlers()
  }

  /**
   * 设置全局错误处理器
   */
  setupGlobalErrorHandlers() {
    // 处理未捕获的错误
    if (typeof wx !== 'undefined') {
      wx.onError && wx.onError((error) => {
        this.handleError('Global Error', error)
      })

      // 处理未处理的Promise拒绝
      wx.onUnhandledRejection && wx.onUnhandledRejection((res) => {
        this.handleError('Unhandled Promise Rejection', res.reason)
      })
    }

    // 处理标准的全局错误
    if (typeof window !== 'undefined') {
      window.addEventListener('error', (event) => {
        this.handleError('Window Error', event.error || event.message)
      })

      window.addEventListener('unhandledrejection', (event) => {
        this.handleError('Unhandled Promise Rejection', event.reason)
      })
    }
  }

  /**
   * 处理错误
   * @param {string} type - 错误类型
   * @param {any} error - 错误信息
   */
  handleError(type, error) {
    const errorInfo = {
      type: type,
      message: this.extractErrorMessage(error),
      timestamp: new Date().toISOString(),
      stack: error && error.stack ? error.stack : null
    }

    // 添加到错误日志
    this.addToErrorLog(errorInfo)

    // 根据错误类型进行特殊处理
    this.handleSpecificError(errorInfo)

    // 输出到控制台
    logger.error(`[${type}]`, error)
  }

  /**
   * 提取错误信息
   * @param {any} error - 错误对象
   * @returns {string}
   */
  extractErrorMessage(error) {
    if (typeof error === 'string') {
      return error
    }
    
    if (error && error.message) {
      return error.message
    }
    
    if (error && error.errMsg) {
      return error.errMsg
    }
    
    return JSON.stringify(error)
  }

  /**
   * 添加到错误日志
   * @param {Object} errorInfo - 错误信息
   */
  addToErrorLog(errorInfo) {
    this.errorLog.push(errorInfo)
    
    // 限制日志大小
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog.shift()
    }
  }

  /**
   * 处理特定类型的错误
   * @param {Object} errorInfo - 错误信息
   */
  handleSpecificError(errorInfo) {
    const message = errorInfo.message.toLowerCase()

    // 处理getUserGrantInfo相关错误
    if (message.includes('getusergrantinfo') || message.includes('gametransfer')) {
      this.handleUserGrantError(errorInfo)
    }

    // 处理文件访问错误
    if (message.includes('no such file or directory') || message.includes('wxfile://')) {
      this.handleFileAccessError(errorInfo)
    }

    // 处理网络相关错误
    if (message.includes('network') || message.includes('request')) {
      this.handleNetworkError(errorInfo)
    }

    // 处理Canvas相关错误
    if (message.includes('canvas') || message.includes('context')) {
      this.handleCanvasError(errorInfo)
    }
  }

  /**
   * 处理用户授权相关错误
   * @param {Object} errorInfo - 错误信息
   */
  handleUserGrantError(errorInfo) {
    logger.warn('用户授权相关错误，使用降级方案')
    
    // 这类错误通常不影响游戏核心功能，可以忽略
    // 或者提供降级的用户体验
  }

  /**
   * 处理文件访问错误
   * @param {Object} errorInfo - 错误信息
   */
  handleFileAccessError(errorInfo) {
    logger.warn('文件访问错误，可能是日志文件相关')
    
    // 文件访问错误通常是系统日志相关，不影响游戏功能
    // 可以尝试清理本地存储或使用替代方案
  }

  /**
   * 处理网络错误
   * @param {Object} errorInfo - 错误信息
   */
  handleNetworkError(errorInfo) {
    logger.warn('网络错误，检查网络连接')
    
    // 可以显示网络错误提示
    // 或者启用离线模式
  }

  /**
   * 处理Canvas错误
   * @param {Object} errorInfo - 错误信息
   */
  handleCanvasError(errorInfo) {
    logger.error('Canvas错误，这可能影响游戏渲染')
    
    // Canvas错误比较严重，可能需要重新初始化
    // 或者降级到简化的渲染模式
  }

  /**
   * 安全调用微信API
   * @param {string} apiName - API名称
   * @param {Object} options - API参数
   * @param {function} fallback - 降级方案
   * @returns {Promise}
   */
  safeWxApiCall(apiName, options = {}, fallback = null) {
    return new Promise((resolve, reject) => {
      try {
        if (typeof wx === 'undefined' || !wx[apiName]) {
          throw new Error(`微信API ${apiName} 不可用`)
        }

        const apiOptions = {
          ...options,
          success: (res) => {
            resolve(res)
          },
          fail: (err) => {
            this.handleError(`WX API Error: ${apiName}`, err)
            
            if (fallback && typeof fallback === 'function') {
              try {
                const fallbackResult = fallback(err)
                resolve(fallbackResult)
              } catch (fallbackError) {
                reject(fallbackError)
              }
            } else {
              reject(err)
            }
          }
        }

        wx[apiName](apiOptions)
      } catch (error) {
        this.handleError(`WX API Call Error: ${apiName}`, error)
        
        if (fallback && typeof fallback === 'function') {
          try {
            const fallbackResult = fallback(error)
            resolve(fallbackResult)
          } catch (fallbackError) {
            reject(fallbackError)
          }
        } else {
          reject(error)
        }
      }
    })
  }

  /**
   * 获取错误日志
   * @returns {Array}
   */
  getErrorLog() {
    return [...this.errorLog]
  }

  /**
   * 清空错误日志
   */
  clearErrorLog() {
    this.errorLog = []
  }

  /**
   * 检查是否有严重错误
   * @returns {boolean}
   */
  hasCriticalErrors() {
    return this.errorLog.some(error => 
      error.type.includes('Canvas') || 
      error.message.includes('critical') ||
      error.message.includes('fatal')
    )
  }

  /**
   * 生成错误报告
   * @returns {string}
   */
  generateErrorReport() {
    const report = {
      timestamp: new Date().toISOString(),
      totalErrors: this.errorLog.length,
      criticalErrors: this.errorLog.filter(e => this.isCriticalError(e)).length,
      recentErrors: this.errorLog.slice(-10),
      systemInfo: this.getSystemInfo()
    }

    return JSON.stringify(report, null, 2)
  }

  /**
   * 判断是否为严重错误
   * @param {Object} error - 错误信息
   * @returns {boolean}
   */
  isCriticalError(error) {
    const criticalKeywords = ['canvas', 'critical', 'fatal', 'crash']
    const message = error.message.toLowerCase()
    return criticalKeywords.some(keyword => message.includes(keyword))
  }

  /**
   * 获取系统信息
   * @returns {Object}
   */
  getSystemInfo() {
    try {
      if (typeof wx !== 'undefined' && wx.getSystemInfoSync) {
        return wx.getSystemInfoSync()
      }
    } catch (error) {
      logger.warn('无法获取系统信息:', error)
    }
    
    return {
      platform: 'unknown',
      version: 'unknown'
    }
  }
}

// CommonJS导出
module.exports = ErrorHandler 