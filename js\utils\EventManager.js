const logger = require('AsyncLogger')

/**
 * 事件管理器
 * 用于管理游戏中的事件监听和触发
 */
class EventManager {
  constructor() {
    this.listeners = new Map()
  }

  /**
   * 添加事件监听器
   * @param {string} eventType - 事件类型
   * @param {function} callback - 回调函数
   * @param {Object} context - 上下文对象
   */
  on(eventType, callback, context = null) {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, [])
    }
    
    this.listeners.get(eventType).push({
      callback,
      context
    })
  }

  /**
   * 移除事件监听器
   * @param {string} eventType - 事件类型
   * @param {function} callback - 回调函数
   * @param {Object} context - 上下文对象
   */
  off(eventType, callback, context = null) {
    if (!this.listeners.has(eventType)) {
      return
    }

    const listeners = this.listeners.get(eventType)
    const index = listeners.findIndex(listener => 
      listener.callback === callback && listener.context === context
    )

    if (index > -1) {
      listeners.splice(index, 1)
    }

    // 如果没有监听器了，删除这个事件类型
    if (listeners.length === 0) {
      this.listeners.delete(eventType)
    }
  }

  /**
   * 触发事件
   * @param {string} eventType - 事件类型
   * @param {*} data - 事件数据
   */
  emit(eventType, data = null) {
    if (!this.listeners.has(eventType)) {
      return
    }

    const listeners = this.listeners.get(eventType)
    listeners.forEach(listener => {
      try {
        if (listener.context) {
          listener.callback.call(listener.context, data)
        } else {
          listener.callback(data)
        }
      } catch (error) {
        logger.error(`事件处理错误 [${eventType}]:`, error)
      }
    })
  }

  /**
   * 移除所有事件监听器
   */
  removeAllListeners() {
    this.listeners.clear()
  }

  /**
   * 移除指定类型的所有监听器
   * @param {string} eventType - 事件类型
   */
  removeListeners(eventType) {
    this.listeners.delete(eventType)
  }

  /**
   * 检查是否有指定类型的监听器
   * @param {string} eventType - 事件类型
   * @returns {boolean}
   */
  hasListeners(eventType) {
    return this.listeners.has(eventType) && this.listeners.get(eventType).length > 0
  }

  /**
   * 获取所有事件类型
   * @returns {Array<string>}
   */
  getEventTypes() {
    return Array.from(this.listeners.keys())
  }

  /**
   * 获取指定事件类型的监听器数量
   * @param {string} eventType - 事件类型
   * @returns {number}
   */
  getListenerCount(eventType) {
    return this.listeners.has(eventType) ? this.listeners.get(eventType).length : 0
  }
}

// CommonJS导出
module.exports = EventManager 