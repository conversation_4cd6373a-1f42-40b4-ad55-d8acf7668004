/**
 * 鸭了个鸭 - 微信小游戏
 * 游戏入口文件
 */

// 全局DEBUG配置 - 设为false关闭所有调试日志
const DEBUG_MODE = false

// 添加调试日志
if (DEBUG_MODE) logger.info('🎮 游戏启动中...')

// 首先加载游戏配置
require('./js/utils/GameConfig.js')
if (DEBUG_MODE) logger.info('⚙️ 游戏配置已加载')

const Main = require('./js/main.js')
const logger = require('js/utils/AsyncLogger')


if (DEBUG_MODE) logger.info('📦 Main类已加载:', typeof Main)

// 启动游戏
try {
  new Main()
  if (DEBUG_MODE) logger.info('✅ 游戏启动成功')
} catch (error) {
  logger.error('❌ 游戏启动失败:', error)
} 