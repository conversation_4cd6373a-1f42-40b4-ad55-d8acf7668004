const logger = require('AsyncLogger')

/**
 * 图片生成器工具类
 * 用于生成可爱的卡通小动物图标
 */
class ImageGenerator {
  
  /**
   * roundRect API兼容性polyfill
   * 解决微信小游戏环境中roundRect方法不存在的问题
   * @param {CanvasRenderingContext2D} ctx - Canvas上下文
   * @param {number} x - 起始x坐标
   * @param {number} y - 起始y坐标  
   * @param {number} width - 宽度
   * @param {number} height - 高度
   * @param {number|Array} radii - 圆角半径
   */
  static drawRoundRect(ctx, x, y, width, height, radii) {
    // 兼容性检查
    if (typeof ctx.roundRect === 'function') {
      try {
        // 尝试使用原生roundRect API
        const radius = Array.isArray(radii) ? radii : [radii]
        ctx.roundRect(x, y, width, height, radius)
        return
      } catch (error) {
        logger.warn('⚠️ 原生roundRect调用失败，使用polyfill模拟:', error.message)
      }
    } else {
      logger.info('ℹ️ 当前环境不支持roundRect API，使用polyfill模拟')
    }
    
    // 使用传统Canvas API模拟roundRect
    const radius = Array.isArray(radii) ? radii[0] : radii
    const r = Math.min(radius, width / 2, height / 2)
    
    ctx.beginPath()
    ctx.moveTo(x + r, y)
    ctx.lineTo(x + width - r, y)
    ctx.arcTo(x + width, y, x + width, y + r, r)
    ctx.lineTo(x + width, y + height - r)
    ctx.arcTo(x + width, y + height, x + width - r, y + height, r)
    ctx.lineTo(x + r, y + height)
    ctx.arcTo(x, y + height, x, y + height - r, r)
    ctx.lineTo(x, y + r)
    ctx.arcTo(x, y, x + r, y, r)
    ctx.closePath()
  }

  /**
   * 创建Canvas图片
   * @param {number} width - 宽度
   * @param {number} height - 高度
   * @returns {HTMLCanvasElement}
   */
  static createCanvas(width = 48, height = 48) {
    const canvas = wx.createOffscreenCanvas ? wx.createOffscreenCanvas() : wx.createCanvas()
    canvas.width = width
    canvas.height = height
    return canvas
  }

  /**
   * 生成超可爱的卡通小鸭子图标
   * @param {string} color - 鸭子颜色 ('yellow', 'white', 'blue', etc.)
   * @returns {HTMLCanvasElement}
   */
  static generateDuck(color = 'yellow') {
    const canvas = this.createCanvas()
    const ctx = canvas.getContext('2d')
    
    // 清空画布并设置背景
    ctx.clearRect(0, 0, 48, 48)
    
    // 绘制圆角背景
    ctx.fillStyle = '#FFF8DC'
    ImageGenerator.drawRoundRect(ctx, 2, 2, 44, 44, 8)
    ctx.fill()
    
    // 设置颜色
    const colors = {
      yellow: '#FFD700',
      white: '#FFFFFF',
      blue: '#87CEEB',
      pink: '#FFB6C1',
      green: '#90EE90'
    }
    
    const duckColor = colors[color] || colors.yellow
    
    // 绘制鸭子身体（更圆润的椭圆）
    ctx.fillStyle = duckColor
    ctx.strokeStyle = '#DAA520'
    ctx.lineWidth = 2
    ctx.beginPath()
    ctx.ellipse(24, 30, 14, 11, 0, 0, 2 * Math.PI)
    ctx.fill()
    ctx.stroke()
    
    // 绘制鸭子头部（更大更圆）
    ctx.beginPath()
    ctx.ellipse(24, 18, 10, 10, 0, 0, 2 * Math.PI)
    ctx.fill()
    ctx.stroke()
    
    // 绘制可爱的嘴巴（更大更突出）
    ctx.fillStyle = '#FF8C00'
    ctx.strokeStyle = '#FF6347'
    ctx.lineWidth = 1.5
    ctx.beginPath()
    ctx.ellipse(18, 18, 4, 2.5, 0, 0, 2 * Math.PI)
    ctx.fill()
    ctx.stroke()
    
    // 绘制超大的卡通眼睛
    ctx.fillStyle = '#FFFFFF'
    ctx.strokeStyle = '#000000'
    ctx.lineWidth = 2
    ctx.beginPath()
    ctx.ellipse(21, 15, 3, 3.5, 0, 0, 2 * Math.PI)
    ctx.fill()
    ctx.stroke()
    
    ctx.beginPath()
    ctx.ellipse(27, 15, 3, 3.5, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    // 绘制眼珠
    ctx.fillStyle = '#000000'
    ctx.beginPath()
    ctx.ellipse(21, 15.5, 1.5, 1.8, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    ctx.beginPath()
    ctx.ellipse(27, 15.5, 1.5, 1.8, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    // 绘制眼睛高光（让眼睛更有神）
    ctx.fillStyle = '#FFFFFF'
    ctx.beginPath()
    ctx.ellipse(21.8, 14.5, 0.8, 1, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    ctx.beginPath()
    ctx.ellipse(27.8, 14.5, 0.8, 1, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    // 绘制可爱的腮红
    ctx.fillStyle = '#FFB6C1'
    ctx.globalAlpha = 0.6
    ctx.beginPath()
    ctx.ellipse(16, 20, 2, 1.5, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    ctx.beginPath()
    ctx.ellipse(32, 20, 2, 1.5, 0, 0, 2 * Math.PI)
    ctx.fill()
    ctx.globalAlpha = 1
    
    // 绘制翅膀（更卡通化）
    ctx.fillStyle = '#F0E68C'
    ctx.strokeStyle = '#DAA520'
    ctx.lineWidth = 1.5
    ctx.beginPath()
    ctx.ellipse(32, 28, 4, 6, Math.PI / 6, 0, 2 * Math.PI)
    ctx.fill()
    ctx.stroke()
    
    // 绘制小脚丫
    ctx.fillStyle = '#FF8C00'
    ctx.beginPath()
    ctx.ellipse(20, 40, 2, 1.5, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    ctx.beginPath()
    ctx.ellipse(28, 40, 2, 1.5, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    return canvas
  }

  /**
   * 生成超可爱的卡通小猫图标
   * @param {string} color - 猫的颜色
   * @returns {HTMLCanvasElement}
   */
  static generateCat(color = 'orange') {
    const canvas = this.createCanvas()
    const ctx = canvas.getContext('2d')
    
    ctx.clearRect(0, 0, 48, 48)
    
    // 绘制圆角背景
    ctx.fillStyle = '#FFF0F5'
    ImageGenerator.drawRoundRect(ctx, 2, 2, 44, 44, 8)
    ctx.fill()
    
    const colors = {
      orange: '#FF8C00',
      gray: '#708090',
      black: '#2F2F2F',
      white: '#FFFFFF',
      brown: '#8B4513'
    }
    
    const catColor = colors[color] || colors.orange
    
    // 绘制猫头（更大更圆）
    ctx.fillStyle = catColor
    ctx.strokeStyle = '#8B4513'
    ctx.lineWidth = 2
    ctx.beginPath()
    ctx.ellipse(24, 24, 15, 13, 0, 0, 2 * Math.PI)
    ctx.fill()
    ctx.stroke()
    
    // 绘制可爱的耳朵（更尖更立体）
    ctx.fillStyle = catColor
    ctx.beginPath()
    ctx.moveTo(15, 15)
    ctx.lineTo(10, 5)
    ctx.lineTo(20, 10)
    ctx.closePath()
    ctx.fill()
    ctx.stroke()
    
    ctx.beginPath()
    ctx.moveTo(33, 15)
    ctx.lineTo(38, 5)
    ctx.lineTo(28, 10)
    ctx.closePath()
    ctx.fill()
    ctx.stroke()
    
    // 绘制耳朵内部（粉色）
    ctx.fillStyle = '#FFB6C1'
    ctx.beginPath()
    ctx.ellipse(15, 12, 2.5, 3, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    ctx.beginPath()
    ctx.ellipse(33, 12, 2.5, 3, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    // 绘制超大的卡通眼睛
    ctx.fillStyle = '#32CD32'
    ctx.strokeStyle = '#000000'
    ctx.lineWidth = 2
    ctx.beginPath()
    ctx.ellipse(19, 20, 4, 5, 0, 0, 2 * Math.PI)
    ctx.fill()
    ctx.stroke()
    
    ctx.beginPath()
    ctx.ellipse(29, 20, 4, 5, 0, 0, 2 * Math.PI)
    ctx.fill()
    ctx.stroke()
    
    // 绘制瞳孔（猫眼特有的竖瞳）
    ctx.fillStyle = '#000000'
    ctx.beginPath()
    ctx.ellipse(19, 20, 1, 4, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    ctx.beginPath()
    ctx.ellipse(29, 20, 1, 4, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    // 绘制眼睛高光
    ctx.fillStyle = '#FFFFFF'
    ctx.beginPath()
    ctx.ellipse(20, 18, 1, 1.5, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    ctx.beginPath()
    ctx.ellipse(30, 18, 1, 1.5, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    // 绘制可爱的鼻子（心形）
    ctx.fillStyle = '#FFB6C1'
    ctx.beginPath()
    ctx.moveTo(24, 26)
    ctx.bezierCurveTo(22, 24, 20, 24, 22, 28)
    ctx.bezierCurveTo(23, 29, 25, 29, 26, 28)
    ctx.bezierCurveTo(28, 24, 26, 24, 24, 26)
    ctx.fill()
    
    // 绘制嘴巴（微笑）
    ctx.strokeStyle = '#000000'
    ctx.lineWidth = 2
    ctx.beginPath()
    ctx.moveTo(24, 28)
    ctx.quadraticCurveTo(20, 32, 18, 30)
    ctx.moveTo(24, 28)
    ctx.quadraticCurveTo(28, 32, 30, 30)
    ctx.stroke()
    
    // 绘制可爱的腮红
    ctx.fillStyle = '#FFB6C1'
    ctx.globalAlpha = 0.6
    ctx.beginPath()
    ctx.ellipse(12, 25, 2.5, 2, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    ctx.beginPath()
    ctx.ellipse(36, 25, 2.5, 2, 0, 0, 2 * Math.PI)
    ctx.fill()
    ctx.globalAlpha = 1
    
    // 绘制胡须（更长更可爱）
    ctx.strokeStyle = '#000000'
    ctx.lineWidth = 1.5
    // 左胡须
    ctx.beginPath()
    ctx.moveTo(10, 22)
    ctx.lineTo(2, 20)
    ctx.moveTo(10, 25)
    ctx.lineTo(2, 25)
    ctx.moveTo(10, 28)
    ctx.lineTo(2, 30)
    // 右胡须
    ctx.moveTo(38, 22)
    ctx.lineTo(46, 20)
    ctx.moveTo(38, 25)
    ctx.lineTo(46, 25)
    ctx.moveTo(38, 28)
    ctx.lineTo(46, 30)
    ctx.stroke()
    
    return canvas
  }

  /**
   * 生成小兔子图标
   * @param {string} color - 兔子颜色
   * @returns {HTMLCanvasElement}
   */
  static generateRabbit(color = 'white') {
    const canvas = this.createCanvas()
    const ctx = canvas.getContext('2d')
    
    ctx.clearRect(0, 0, 64, 64)
    
    const colors = {
      white: '#FFFFFF',
      gray: '#C0C0C0',
      brown: '#D2B48C',
      pink: '#FFB6C1'
    }
    
    const rabbitColor = colors[color] || colors.white
    
    // 绘制兔子头部
    ctx.fillStyle = rabbitColor
    ctx.beginPath()
    ctx.ellipse(32, 35, 14, 12, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    // 绘制长耳朵
    ctx.beginPath()
    ctx.ellipse(24, 18, 4, 12, -Math.PI / 6, 0, 2 * Math.PI)
    ctx.fill()
    
    ctx.beginPath()
    ctx.ellipse(40, 18, 4, 12, Math.PI / 6, 0, 2 * Math.PI)
    ctx.fill()
    
    // 绘制耳朵内部
    ctx.fillStyle = '#FFB6C1'
    ctx.beginPath()
    ctx.ellipse(24, 18, 2, 6, -Math.PI / 6, 0, 2 * Math.PI)
    ctx.fill()
    
    ctx.beginPath()
    ctx.ellipse(40, 18, 2, 6, Math.PI / 6, 0, 2 * Math.PI)
    ctx.fill()
    
    // 绘制眼睛
    ctx.fillStyle = '#000000'
    ctx.beginPath()
    ctx.ellipse(27, 30, 3, 3, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    ctx.beginPath()
    ctx.ellipse(37, 30, 3, 3, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    // 绘制眼睛高光
    ctx.fillStyle = '#FFFFFF'
    ctx.beginPath()
    ctx.ellipse(28, 29, 1, 1, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    ctx.beginPath()
    ctx.ellipse(38, 29, 1, 1, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    // 绘制鼻子
    ctx.fillStyle = '#FFB6C1'
    ctx.beginPath()
    ctx.ellipse(32, 35, 2, 1, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    // 绘制嘴巴
    ctx.strokeStyle = '#000000'
    ctx.lineWidth = 1
    ctx.beginPath()
    ctx.moveTo(32, 37)
    ctx.lineTo(29, 40)
    ctx.moveTo(32, 37)
    ctx.lineTo(35, 40)
    ctx.stroke()
    
    // 绘制前牙
    ctx.fillStyle = '#FFFFFF'
    ctx.fillRect(30, 40, 2, 4)
    ctx.fillRect(32, 40, 2, 4)
    
    // 绘制边框
    ctx.strokeStyle = '#000000'
    ctx.lineWidth = 0.5
    ctx.strokeRect(30, 40, 2, 4)
    ctx.strokeRect(32, 40, 2, 4)
    
    return canvas
  }

  /**
   * 生成小狗图标
   * @param {string} color - 狗的颜色
   * @returns {HTMLCanvasElement}
   */
  static generateDog(color = 'brown') {
    const canvas = this.createCanvas()
    const ctx = canvas.getContext('2d')
    
    ctx.clearRect(0, 0, 64, 64)
    
    const colors = {
      brown: '#8B4513',
      golden: '#DAA520',
      black: '#2F2F2F',
      white: '#FFFFFF'
    }
    
    const dogColor = colors[color] || colors.brown
    
    // 绘制狗头
    ctx.fillStyle = dogColor
    ctx.beginPath()
    ctx.ellipse(32, 32, 16, 14, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    // 绘制耳朵
    ctx.beginPath()
    ctx.ellipse(20, 22, 6, 10, -Math.PI / 4, 0, 2 * Math.PI)
    ctx.fill()
    
    ctx.beginPath()
    ctx.ellipse(44, 22, 6, 10, Math.PI / 4, 0, 2 * Math.PI)
    ctx.fill()
    
    // 绘制嘴部（椭圆形）
    ctx.fillStyle = colors.white || '#FFFFFF'
    ctx.beginPath()
    ctx.ellipse(32, 40, 8, 6, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    // 绘制眼睛
    ctx.fillStyle = '#000000'
    ctx.beginPath()
    ctx.ellipse(26, 26, 3, 3, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    ctx.beginPath()
    ctx.ellipse(38, 26, 3, 3, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    // 绘制眼睛高光
    ctx.fillStyle = '#FFFFFF'
    ctx.beginPath()
    ctx.ellipse(27, 25, 1, 1, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    ctx.beginPath()
    ctx.ellipse(39, 25, 1, 1, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    // 绘制鼻子
    ctx.fillStyle = '#000000'
    ctx.beginPath()
    ctx.ellipse(32, 38, 2, 2, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    // 绘制嘴巴
    ctx.strokeStyle = '#000000'
    ctx.lineWidth = 2
    ctx.beginPath()
    ctx.moveTo(32, 40)
    ctx.lineTo(28, 44)
    ctx.moveTo(32, 40)
    ctx.lineTo(36, 44)
    ctx.stroke()
    
    // 绘制舌头
    ctx.fillStyle = '#FF69B4'
    ctx.beginPath()
    ctx.ellipse(32, 47, 3, 2, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    return canvas
  }

  /**
   * 生成小熊图标
   * @param {string} color - 熊的颜色
   * @returns {HTMLCanvasElement}
   */
  static generateBear(color = 'brown') {
    const canvas = this.createCanvas()
    const ctx = canvas.getContext('2d')
    
    ctx.clearRect(0, 0, 64, 64)
    
    const colors = {
      brown: '#8B4513',
      black: '#2F2F2F',
      white: '#FFFFFF',
      panda: '#FFFFFF'
    }
    
    const bearColor = colors[color] || colors.brown
    
    // 绘制熊头
    ctx.fillStyle = bearColor
    ctx.beginPath()
    ctx.ellipse(32, 32, 16, 16, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    // 绘制耳朵
    ctx.beginPath()
    ctx.ellipse(20, 20, 6, 6, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    ctx.beginPath()
    ctx.ellipse(44, 20, 6, 6, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    // 如果是熊猫，添加黑色眼圈
    if (color === 'panda') {
      ctx.fillStyle = '#000000'
      ctx.beginPath()
      ctx.ellipse(26, 28, 6, 8, 0, 0, 2 * Math.PI)
      ctx.fill()
      
      ctx.beginPath()
      ctx.ellipse(38, 28, 6, 8, 0, 0, 2 * Math.PI)
      ctx.fill()
      
      // 耳朵也是黑色
      ctx.beginPath()
      ctx.ellipse(20, 20, 6, 6, 0, 0, 2 * Math.PI)
      ctx.fill()
      
      ctx.beginPath()
      ctx.ellipse(44, 20, 6, 6, 0, 0, 2 * Math.PI)
      ctx.fill()
    }
    
    // 绘制眼睛
    ctx.fillStyle = '#000000'
    ctx.beginPath()
    ctx.ellipse(26, 28, 3, 3, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    ctx.beginPath()
    ctx.ellipse(38, 28, 3, 3, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    // 绘制眼睛高光
    ctx.fillStyle = '#FFFFFF'
    ctx.beginPath()
    ctx.ellipse(27, 27, 1, 1, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    ctx.beginPath()
    ctx.ellipse(39, 27, 1, 1, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    // 绘制鼻子
    ctx.fillStyle = '#000000'
    ctx.beginPath()
    ctx.ellipse(32, 35, 3, 2, 0, 0, 2 * Math.PI)
    ctx.fill()
    
    // 绘制嘴巴
    ctx.strokeStyle = '#000000'
    ctx.lineWidth = 2
    ctx.beginPath()
    ctx.moveTo(32, 37)
    ctx.lineTo(28, 40)
    ctx.moveTo(32, 37)
    ctx.lineTo(36, 40)
    ctx.stroke()
    
    return canvas
  }

  /**
   * 将Canvas转换为Base64 DataURL
   * @param {HTMLCanvasElement} canvas
   * @returns {string}
   */
  static canvasToDataURL(canvas) {
    return canvas.toDataURL('image/png')
  }

  /**
   * 生成所有动物图标
   * @returns {Object} 包含所有动物图标的对象
   */
  static generateAllAnimals() {
    return {
      duck_yellow: this.generateDuck('yellow'),
      duck_white: this.generateDuck('white'),
      duck_blue: this.generateDuck('blue'),
      cat_orange: this.generateCat('orange'),
      cat_gray: this.generateCat('gray'),
      rabbit_white: this.generateRabbit('white'),
      rabbit_brown: this.generateRabbit('brown'),
      dog_brown: this.generateDog('brown'),
      dog_golden: this.generateDog('golden'),
      bear_brown: this.generateBear('brown'),
      bear_panda: this.generateBear('panda')
    }
  }
}

// CommonJS导出
module.exports = ImageGenerator 