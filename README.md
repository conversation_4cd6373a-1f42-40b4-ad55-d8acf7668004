# 鸭了个鸭 - 微信小游戏

一个模仿羊了个羊玩法的三消类微信小游戏。

## 游戏特点

- 🦆 以鸭子为主题的可爱游戏
- 🎯 经典的三消玩法
- 📱 适配微信小游戏平台
- 🎨 使用Canvas 2D渲染
- 🎮 简单易上手的操作

## 游戏玩法

1. **点击方块**: 点击最上层的可见方块
2. **移动到槽位**: 点击的方块会移动到底部槽位
3. **三消机制**: 当槽位中有3个相同类型的方块时自动消除
4. **胜利条件**: 清除游戏板上所有方块
5. **失败条件**: 槽位满了（7个方块）且无法消除

## 运行方法

### 使用微信开发者工具

1. 下载并安装微信开发者工具
2. 选择"小游戏"项目类型
3. 导入本项目目录
4. 设置项目配置：
   - AppID: 可使用测试号
   - 项目名称: 鸭了个鸭
5. 点击"编译"按钮运行游戏

### 项目结构

```
duckduck/
├── game.js              # 游戏入口文件
├── game.json           # 游戏配置文件
├── project.config.json # 项目配置文件
├── js/                 # 源代码目录
│   ├── main.js        # 主控制器
│   ├── game/          # 游戏逻辑
│   │   ├── Block.js   # 方块类
│   │   ├── Board.js   # 游戏板类
│   │   └── Slot.js    # 槽位类
│   └── utils/         # 工具类
│       ├── Utils.js
│       ├── ResourceLoader.js
│       └── EventManager.js
├── images/            # 图片资源（待添加）
└── audio/            # 音频资源（待添加）
```

## 技术特点

- **框架**: 微信小游戏原生开发
- **渲染**: Canvas 2D API
- **语言**: JavaScript ES6+
- **架构**: 模块化设计，易于扩展
- **适配**: 支持不同屏幕分辨率

## 当前版本功能

✅ **已实现**:
- 基础游戏框架
- Canvas渲染系统
- 方块系统和动画
- 三消逻辑
- 槽位管理
- 触摸交互
- 关卡系统（3个预设关卡）
- 胜负判定
- 游戏状态管理
- **🎨 UI美化系统（新增）**
  - 菜单界面美化（标题动画、装饰元素、用户信息卡片）
  - 关卡选择界面美化（渐变背景、3D按钮、动态光效）
  - 游戏界面UI优化（美化信息栏、动态分数显示）
  - **🎉 关卡完成页面美化（最新）**
    - 庆祝动画效果（烟花、彩带、星星闪烁、光环扩散）
    - 美化的UI设计（渐变背景、发光文字、卡片式统计）
    - 流畅的入场动画（分阶段显示、缓动效果）
    - 交互体验优化（跳过动画、按钮交互）
    - 高性能粒子系统（对象池、性能优化）
  - 流畅动画效果（缓动函数、性能优化）
  - 现代化视觉设计（渐变、阴影、圆角）

🚧 **待完善**:
- 图片资源
- 音效系统
- 道具功能（移出、撤回、洗牌）
- 更多关卡设计
- 数据持久化

## 开发说明

### 核心类介绍

1. **Main**: 主控制器，管理游戏流程
2. **Block**: 方块类，包含位置、类型、动画等
3. **Board**: 游戏板，管理方块布局和层级
4. **Slot**: 槽位，管理底部容器和三消逻辑
5. **Utils**: 工具类，提供常用函数
6. **ResourceLoader**: 资源加载器
7. **ParticleSystem**: 粒子系统，管理庆祝动画效果
8. **WinScreen**: 胜利页面组件，美化关卡完成界面

### UI美化特性详解

### 🎨 菜单界面美化
- **增强版标题**: 发光效果、渐变色彩、闪烁动画
- **浮动装饰**: 动态鸭子图标、星星装饰元素
- **用户信息卡片**: 渐变背景、阴影效果、圆角设计
- **脉冲开始按钮**: 动态缩放、渐变背景、悬停效果

### 🎯 关卡选择界面美化
- **丰富标题区域**: 多层渐变背景、动态装饰光效
- **3D关卡按钮**: 立体效果、悬浮动画、高光反射
- **增强星级显示**: 金色星星、描边效果、动态评价
- **美化返回按钮**: 悬浮效果、渐变背景、圆角设计

### 🎮 游戏界面UI优化
- **美化信息栏**: 渐变背景、装饰光效、圆角边框
- **动态分数显示**: 脉冲效果、阴影文字、背景高亮
- **移动次数统计**: 图标化显示、背景美化
- **关卡信息优化**: 背景卡片、文字阴影效果

### 🎉 关卡完成页面美化（最新功能）
- **庆祝动画效果**:
  - 烟花爆炸：多重彩色粒子爆炸效果
  - 彩带飘落：旋转彩带从顶部飘落
  - 星星闪烁：随机分布的金色星星闪烁
  - 光环扩散：从中心向外扩散的光环效果
- **美化UI设计**:
  - 渐变背景：金色到紫色的庆祝主题渐变
  - 发光文字：标题具有金色发光和阴影效果
  - 卡片式统计：统计信息显示在美观的卡片中
  - 现代化按钮：渐变背景的圆角按钮，具有脉冲效果
- **流畅动画**:
  - 分阶段入场：元素按时序依次出现
  - 缓动效果：使用专业缓动函数确保流畅
  - 数字递增：统计数字从0递增到实际值
- **交互优化**:
  - 跳过功能：1秒后可点击跳过动画
  - 按钮交互：支持精确的点击检测
  - 性能优化：使用对象池管理粒子，确保流畅运行

### ⚡ 性能优化
- 使用requestAnimationFrame确保流畅动画
- 优化渲染循环，减少不必要的重绘
- 合理使用globalAlpha和save/restore
- 动画效果不影响游戏核心性能

## 扩展建议

- 添加更多方块类型和特殊道具
- 实现排行榜和社交功能
- 添加更丰富的视觉和音效
- 实现无限关卡生成算法
- 为动画添加对应的音效同步
- 添加更多粒子类型（心形、钻石等）
- 实现成就系统和不同庆祝主题
- 添加社交分享功能

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！ 